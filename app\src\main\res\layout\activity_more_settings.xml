<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="#EFF3F6">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">

        <!-- 标题栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="30dp">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/icon_back_black_fine"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:padding="4dp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/str_more_settings"
                android:textColor="@color/color_333333"
                android:textSize="20sp"
                android:textStyle="bold"
                android:gravity="center" />

            <View
                android:layout_width="24dp"
                android:layout_height="24dp" />

        </LinearLayout>

        <!-- 首页语音引导 -->
        <LinearLayout
            android:id="@+id/ll_proactively_greet"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="@drawable/main_bt_common_bg"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="15dp"
            android:layout_marginBottom="15dp">

            <TextView
                android:id="@+id/tv_proactively_greet"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/str_proactively_greet"
                android:textColor="@color/color_333333"
                android:textSize="17sp"
                android:includeFontPadding="false" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/switch_proactively_greet"
                android:layout_width="70dp"
                android:layout_height="28dp"
                app:switchMinWidth="70dp"
                android:splitTrack="true"
                android:thumb="@drawable/switch_mask_therapy_thumb"
                app:track="@drawable/switch_mask_therapy_style"
                android:thumbTint="@color/white"
                app:thumbTint="@color/white"
                android:trackTint="@color/selector_common_switch_compat_track_tint"
                app:trackTint="@color/selector_common_switch_compat_track_tint" />

        </LinearLayout>

        <!-- 模型版本 -->
        <LinearLayout
            android:id="@+id/ll_model_version"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="@drawable/main_bt_common_bg"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="15dp"
            android:layout_marginBottom="15dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/str_model_version"
                android:textColor="@color/color_333333"
                android:textSize="17sp"
                android:includeFontPadding="false" />

            <TextView
                android:id="@+id/tv_model_version"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="V1.0.0.1"
                android:textColor="@color/color_666666"
                android:textSize="15sp"
                android:layout_marginEnd="10dp" />

            <TextView
                android:id="@+id/btn_update_model"
                android:layout_width="60dp"
                android:layout_height="32dp"
                android:text="@string/str_update"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:background="@drawable/main_bt_common_bg"
                android:backgroundTint="@color/color_4A90E2"
                android:gravity="center"
                android:clickable="true"
                android:focusable="true" />

        </LinearLayout>

        <!-- 测试版版本 -->
        <LinearLayout
            android:id="@+id/ll_test_version"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="@drawable/main_bt_common_bg"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="15dp"
            android:layout_marginBottom="15dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/str_test_version"
                android:textColor="@color/color_333333"
                android:textSize="17sp"
                android:includeFontPadding="false" />

            <TextView
                android:id="@+id/tv_test_version"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="V2.0.1"
                android:textColor="@color/color_666666"
                android:textSize="15sp"
                android:layout_marginEnd="10dp" />

            <TextView
                android:id="@+id/btn_update_test"
                android:layout_width="60dp"
                android:layout_height="32dp"
                android:text="@string/str_update"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:background="@drawable/main_bt_common_bg"
                android:backgroundTint="@color/color_4A90E2"
                android:gravity="center"
                android:clickable="true"
                android:focusable="true" />

        </LinearLayout>

        <!-- 数据缓存 -->
        <LinearLayout
            android:id="@+id/ll_data_cache"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="@drawable/main_bt_common_bg"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="15dp"
            android:layout_marginBottom="15dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/str_data_cache"
                android:textColor="@color/color_333333"
                android:textSize="17sp"
                android:includeFontPadding="false" />

            <TextView
                android:id="@+id/tv_data_cache_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_none"
                android:textColor="@color/color_666666"
                android:textSize="15sp"
                android:layout_marginEnd="10dp" />

            <TextView
                android:id="@+id/btn_upload_cache"
                android:layout_width="60dp"
                android:layout_height="32dp"
                android:text="@string/str_upload"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:background="@drawable/main_bt_common_bg"
                android:backgroundTint="@color/color_4A90E2"
                android:gravity="center"
                android:clickable="true"
                android:focusable="true" />

        </LinearLayout>

        <!-- 指尖式数据采集 -->
        <LinearLayout
            android:id="@+id/ll_fingertip_collection"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="@drawable/main_bt_common_bg"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="15dp"
            android:layout_marginBottom="15dp">

            <TextView
                android:id="@+id/tv_fingertip_collection"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/str_fingertip_data_collection"
                android:textColor="@color/color_333333"
                android:textSize="17sp"
                android:includeFontPadding="false" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/switch_fingertip_collection"
                android:layout_width="70dp"
                android:layout_height="28dp"
                app:switchMinWidth="70dp"
                android:splitTrack="true"
                android:thumb="@drawable/switch_mask_therapy_thumb"
                app:track="@drawable/switch_mask_therapy_style"
                android:thumbTint="@color/white"
                app:thumbTint="@color/white"
                android:trackTint="@color/selector_common_switch_compat_track_tint"
                app:trackTint="@color/selector_common_switch_compat_track_tint" />

        </LinearLayout>

        <!-- 采集器编号 -->
        <LinearLayout
            android:id="@+id/ll_collector_number"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="@drawable/main_bt_common_bg"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="15dp"
            android:layout_marginBottom="15dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/str_collector_number"
                android:textColor="@color/color_333333"
                android:textSize="17sp"
                android:includeFontPadding="false" />

            <TextView
                android:id="@+id/tv_collector_number"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="000477"
                android:textColor="@color/color_666666"
                android:textSize="15sp"
                android:layout_marginEnd="10dp" />

            <TextView
                android:id="@+id/btn_change_collector"
                android:layout_width="60dp"
                android:layout_height="32dp"
                android:text="@string/str_change"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:background="@drawable/main_bt_common_bg"
                android:backgroundTint="@color/color_4A90E2"
                android:gravity="center"
                android:clickable="true"
                android:focusable="true" />

        </LinearLayout>

    </LinearLayout>

</androidx.core.widget.NestedScrollView>